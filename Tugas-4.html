<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video VR</title>
    <script src="https://aframe.io/releases/1.0.4/aframe.min.js"></script>

    <script>
        AFRAME.registerComponent('play-pause', {
            init: function () {
                var myVideo = document.querySelector('#myvideo');
                var videoControls = document.querySelector('#videoControls');
                myVideo.pause();

                this.el.addEventListener('click', function () {
                    if (myVideo.paused) {
                        myVideo.play();
                        videoControls.setAttribute('src', '#pause');
                    } else {
                        myVideo.pause();
                        videoControls.setAttribute('src', '#play');
                    }
                });
            }
        });
    </script>

</head>

<body>
    <a-scene>
        <a-assets>
            <img id="play" src="assets/play.png">
            <img id="pause" src="assets/pause.png">
            <video id="myvideo" src="assets/kroncong.mp4" autoplay loop="true" preload="auto"></video>
        </a-assets>

        <a-camera>
            <a-cursor color="white"></a-cursor>
        </a-camera>

        <a-videosphere src="#myvideo" rotation="0 -80 0"></a-videosphere>

        <a-image id="videoControls" src="#play" position="0 1 -2" scale="0.2 0.2 1" play-pause>
        </a-image>

    </a-scene>
</body>

</html>