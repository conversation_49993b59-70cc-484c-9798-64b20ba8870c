<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video VR</title>
    <script src="https://aframe.io/releases/1.0.4/aframe.min.js"></script>

    <script>
        // Component for play/pause functionality
        AFRAME.registerComponent('play-pause', {
            init: function () {
                var myVideo = document.querySelector('#myvideo');
                var videoControls = document.querySelector('#videoControls');
                myVideo.pause();

                this.el.addEventListener('click', function () {
                    if (myVideo.paused) {
                        myVideo.play();
                        videoControls.setAttribute('src', '#pause');
                    } else {
                        myVideo.pause();
                        videoControls.setAttribute('src', '#play');
                    }
                });
            }
        });

        // Component for switching between videos
        AFRAME.registerComponent('video-switch', {
            init: function () {
                var myVideo = document.querySelector('#myvideo');
                var videosphere = document.querySelector('a-videosphere');
                var videoControls = document.querySelector('#videoControls');
                var currentVideo = 0; // 0 for kroncong, 1 for cinematic
                var videos = ['assets/cinematic2.mp4', 'assets/cinematic.mp4'];
                var videoNames = ['Kroncong', 'Cinematic'];

                this.el.addEventListener('click', function () {
                    // Pause current video
                    myVideo.pause();

                    // Switch to next video
                    currentVideo = (currentVideo + 1) % videos.length;

                    // Update video source
                    myVideo.src = videos[currentVideo];
                    myVideo.load(); // Reload the video element

                    // Update videosphere source
                    videosphere.setAttribute('src', '#myvideo');

                    // Update play button
                    videoControls.setAttribute('src', '#play');

                    console.log('Switched to: ' + videos[currentVideo]);
                });
            }
        });
    </script>

</head>

<body>
    <a-scene>
        <a-assets>
            <img id="play" src="assets/play.png">
            <img id="pause" src="assets/pause.png">
            <img id="exchange" src="assets/exchange.png">
            <video id="myvideo" src="assets/cinematic2.mp4" autoplay loop="true" preload="auto"></video>
        </a-assets>

        <a-camera>
            <a-cursor color="white"></a-cursor>
        </a-camera>

        <a-videosphere src="#myvideo" rotation="0 -80 0"></a-videosphere>

        <a-image id="videoControls" src="#play" position="0 1 -2" scale="0.2 0.2 1" play-pause>
        </a-image>

        <!-- Video Switch Button -->
        <a-image id="videoSwitch" src="#exchange" position="0 0.5 -2" scale="0.2 0.2 1" video-switch>
        </a-image>

    </a-scene>
</body>

</html>